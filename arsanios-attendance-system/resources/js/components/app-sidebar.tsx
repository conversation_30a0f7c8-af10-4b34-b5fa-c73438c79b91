import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link } from '@inertiajs/react';
import { BarChart3, BookOpen, ClipboardCheck, Folder, Gift, LayoutGrid, Settings, UserPlus, Users } from 'lucide-react';
import AppLogo from './app-logo';

const mainNavItems: NavItem[] = [
    {
        title: 'لوحة التحكم',
        href: '/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'تسجيل الحضور',
        href: '/attendance',
        icon: ClipboardCheck,
    },
    {
        title: 'إضافة عضو',
        href: '/add-user',
        icon: UserPlus,
    },
    {
        title: 'إدارة الأعضاء',
        href: '/users',
        icon: Users,
    },
    {
        title: 'أعياد الميلاد',
        href: '/birthdays',
        icon: Gift,
    },
    {
        title: 'التقارير',
        href: '/reports',
        icon: BarChart3,
    },
    {
        title: 'الإعدادات',
        href: '/app-settings',
        icon: Settings,
    },
];

const footerNavItems: NavItem[] = [
    {
        title: 'المستودع',
        href: 'https://github.com/laravel/react-starter-kit',
        icon: Folder,
    },
    {
        title: 'التوثيق',
        href: 'https://laravel.com/docs/starter-kits#react',
        icon: BookOpen,
    },
];

export function AppSidebar() {
    return (
        <Sidebar side="right" collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/dashboard" prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain items={mainNavItems} />
            </SidebarContent>

            <SidebarFooter>
                <NavFooter items={footerNavItems} className="mt-auto" />
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
