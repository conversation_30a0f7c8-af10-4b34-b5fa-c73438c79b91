'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useRTL } from '@/contexts/rtl-context';
import { useToast } from '@/hooks/use-toast';
import AppLayout from '@/layouts/app-layout';
import { cn } from '@/lib/utils';
import { useAppStore } from '@/stores/app-store';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { Calendar, CheckCircle, Download, GraduationCap, Loader2, MapPin, Phone, QrCode, Save, Share, User, UserPlus, X } from 'lucide-react';
import { useCallback, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

// Enhanced form validation schema with better error messages and validation rules
const formSchema = z.object({
    name: z
        .string()
        .min(2, 'الاسم يجب أن يكون أكثر من حرفين')
        .max(50, 'الاسم لا يجب أن يتجاوز 50 حرف')
        .regex(/^[\u0600-\u06FF\s]+$/, 'الاسم يجب أن يحتوي على أحرف عربية فقط'),
    phone: z
        .string()
        .min(11, 'رقم الهاتف يجب أن يكون 11 رقم')
        .max(11, 'رقم الهاتف يجب أن يكون 11 رقم')
        .regex(/^01[0-9]{9}$/, 'رقم الهاتف يجب أن يبدأ بـ 01 ويحتوي على 11 رقم'),
    gender: z.enum(['male', 'female'], { message: 'يرجى اختيار النوع' }),
    year: z.enum(['1', '2', '3', '4'], { message: 'يرجى اختيار السنة الدراسية' }),
    college: z.string().min(2, 'اسم الكلية مطلوب').max(100, 'اسم الكلية لا يجب أن يتجاوز 100 حرف'),
    department: z.string().min(2, 'اسم القسم مطلوب').max(100, 'اسم القسم لا يجب أن يتجاوز 100 حرف'),
    birthdate: z
        .string()
        .min(1, 'تاريخ الميلاد مطلوب')
        .refine((date) => {
            const birthDate = new Date(date);
            const today = new Date();
            const age = today.getFullYear() - birthDate.getFullYear();
            return age >= 16 && age <= 35;
        }, 'العمر يجب أن يكون بين 16 و 35 سنة'),
    address: z.string().min(10, 'العنوان يجب أن يكون أكثر تفصيلاً (10 أحرف على الأقل)').max(200, 'العنوان لا يجب أن يتجاوز 200 حرف'),
    facebook_url: z
        .string()
        .optional()
        .refine((url) => !url || url.includes('facebook.com') || url.includes('fb.com'), 'رابط الفيسبوك غير صحيح'),
    first_attendance_date: z
        .string()
        .min(1, 'تاريخ أول حضور مطلوب')
        .refine((date) => {
            const attendanceDate = new Date(date);
            const today = new Date();
            return attendanceDate <= today;
        }, 'تاريخ أول حضور لا يمكن أن يكون في المستقبل'),
});

type FormData = z.infer<typeof formSchema>;

// Enhanced loading and submission states
interface SubmissionState {
    isSubmitting: boolean;
    isSuccess: boolean;
    isError: boolean;
    progress: number;
    generatedQR: string | null;
}

function AddUserPage() {
    const { addUser } = useAppStore();
    const { isRTL, direction } = useRTL();
    const { toast } = useToast();

    // Enhanced state management
    const [submissionState, setSubmissionState] = useState<SubmissionState>({
        isSubmitting: false,
        isSuccess: false,
        isError: false,
        progress: 0,
        generatedQR: null,
    });

    // Memoized default values for performance
    const defaultValues = useMemo(
        () => ({
            name: '',
            phone: '',
            gender: undefined,
            year: undefined,
            college: '',
            department: '',
            birthdate: '',
            address: '',
            facebook_url: '',
            first_attendance_date: format(new Date(), 'yyyy-MM-dd'),
        }),
        [],
    );

    const form = useForm<FormData>({
        resolver: zodResolver(formSchema),
        mode: 'onChange', // Enable real-time validation
        defaultValues,
    });

    // Memoized required fields for performance
    const requiredFields = useMemo(
        () => ['name', 'phone', 'gender', 'year', 'college', 'department', 'birthdate', 'address', 'first_attendance_date'],
        [],
    );

    // Calculate form completion progress - optimized for performance
    const watchedValues = form.watch();
    const formProgress = useMemo(() => {
        const completedFields = requiredFields.filter((field) => {
            const value = watchedValues[field as keyof FormData];
            return value && value.toString().trim() !== '';
        });
        return Math.round((completedFields.length / requiredFields.length) * 100);
    }, [watchedValues, requiredFields]);

    // Reset form function
    const resetForm = useCallback(() => {
        form.reset();
        setSubmissionState({
            isSubmitting: false,
            isSuccess: false,
            isError: false,
            progress: 0,
            generatedQR: null,
        });
    }, [form]);

    // Enhanced form submission with loading states
    const onSubmit = useCallback(
        async (data: FormData) => {
            setSubmissionState((prev) => ({ ...prev, isSubmitting: true, progress: 0 }));

            try {
                // Simulate progress steps
                setSubmissionState((prev) => ({ ...prev, progress: 25 }));
                await new Promise((resolve) => setTimeout(resolve, 500));

                setSubmissionState((prev) => ({ ...prev, progress: 50 }));
                const userData = {
                    ...data,
                    year: Number.parseInt(data.year) as 1 | 2 | 3 | 4,
                };

                setSubmissionState((prev) => ({ ...prev, progress: 75 }));
                await new Promise((resolve) => setTimeout(resolve, 500));

                addUser(userData);
                const qrCode = `QR_${Date.now()}`;

                setSubmissionState({
                    isSubmitting: false,
                    isSuccess: true,
                    isError: false,
                    progress: 100,
                    generatedQR: qrCode,
                });

                toast({
                    title: 'تم إضافة العضو بنجاح!',
                    description: 'تم إنشاء حساب العضو وتوليد QR Code الخاص به',
                    variant: 'default',
                });

                // Auto-reset after 5 seconds
                setTimeout(() => {
                    resetForm();
                }, 5000);
            } catch {
                setSubmissionState({
                    isSubmitting: false,
                    isSuccess: false,
                    isError: true,
                    progress: 0,
                    generatedQR: null,
                });

                toast({
                    title: 'حدث خطأ أثناء إضافة العضو',
                    description: 'يرجى المحاولة مرة أخرى',
                    variant: 'destructive',
                });
            }
        },
        [addUser, toast, resetForm],
    );

    // Success state component
    if (submissionState.isSuccess) {
        return (
            <div className="page-transition font-cairo space-y-6 p-6" dir={direction}>
                <div className="mx-auto max-w-2xl">
                    <Card className="animate-scale-in border-green-200 bg-green-50">
                        <CardHeader className="text-center">
                            <div className="mb-4 flex justify-center">
                                <div className="flex h-16 w-16 items-center justify-center rounded-full bg-green-500">
                                    <CheckCircle className="h-8 w-8 text-white" />
                                </div>
                            </div>
                            <CardTitle className="text-2xl text-green-800">تم إضافة العضو بنجاح!</CardTitle>
                            <CardDescription className="text-green-600">تم إنشاء حساب العضو وتوليد QR Code الخاص به</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6 text-center">
                            <div className="mx-auto flex h-48 w-48 items-center justify-center rounded-lg border-2 border-green-200 bg-white">
                                <QrCode className="h-24 w-24 text-green-500" />
                            </div>
                            <div>
                                <h3 className="mb-2 font-medium text-green-800">QR Code جاهز للاستخدام</h3>
                                <p className="text-sm text-green-600">يمكن للعضو استخدام هذا الرمز لتسجيل الحضور</p>
                            </div>
                            <div className={cn('flex justify-center gap-3')}>
                                <Button variant="outline" className="border-green-300 bg-transparent text-green-700">
                                    <Download className={cn('h-4 w-4', isRTL ? 'ml-2' : 'mr-2')} />
                                    تحميل QR
                                </Button>
                                <Button variant="outline" className="border-green-300 bg-transparent text-green-700">
                                    <Share className={cn('h-4 w-4', isRTL ? 'ml-2' : 'mr-2')} />
                                    مشاركة
                                </Button>
                                <Button className="bg-green-600 hover:bg-green-700" onClick={resetForm}>
                                    إضافة عضو آخر
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        );
    }

    return (
        <div className="font-cairo min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 transition-all duration-500" dir={direction}>
            <div className="container mx-auto px-4 py-8 sm:px-6 lg:px-8">
                {/* Enhanced Header with animations */}
                <div className="animate-fade-in mb-8">
                    <div className="mb-4 flex items-center gap-4">
                        <div className="group relative">
                            <div className="absolute -inset-1 rounded-xl bg-gradient-to-r from-green-500 to-emerald-500 opacity-75 blur transition duration-300 group-hover:opacity-100"></div>
                            <div className="relative rounded-xl bg-gradient-to-r from-green-500 to-emerald-500 p-3 transition-transform duration-300 group-hover:scale-105">
                                <UserPlus className="h-7 w-7 text-white" />
                            </div>
                        </div>
                        <div>
                            <h1 className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text text-right text-4xl font-bold text-transparent sm:text-5xl">
                                إضافة عضو جديد
                            </h1>
                            <p className="mt-2 text-right text-lg text-gray-600">إضافة عضو جديد لنظام حضور الشباب مع توليد QR Code</p>
                        </div>
                    </div>

                    {/* Enhanced Progress indicator with RTL support */}
                    <div className="mb-6">
                        <div className="mb-2 flex items-center justify-between">
                            <span className="text-right text-sm font-medium text-gray-700">اكتمال النموذج: {formProgress}%</span>
                            <span className="text-sm text-gray-500">
                                {formProgress === 100 ? 'مكتمل!' : 'يرجى إكمال البيانات المطلوبة'}
                            </span>
                        </div>
                        <Progress value={formProgress} className="rtl-progress h-2 transition-all duration-500" />
                    </div>
                </div>

                {/* Enhanced Form Card */}
                <div className="mx-auto max-w-4xl">
                    <Card className="group relative overflow-hidden border-0 bg-white/80 shadow-xl backdrop-blur-sm transition-all duration-500 hover:shadow-2xl">
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 transition-opacity duration-500 group-hover:opacity-100"></div>

                        <CardHeader className="relative border-b border-gray-100 bg-white/50 backdrop-blur-sm">
                            <CardTitle className="flex items-center gap-3 text-xl font-semibold text-gray-900">
                                <div className="rounded-lg bg-blue-100 p-2">
                                    <User className="h-5 w-5 text-blue-600" />
                                </div>
                                بيانات العضو الجديد
                            </CardTitle>
                            <CardDescription className="text-right text-gray-600">
                                يرجى ملء جميع البيانات المطلوبة لإضافة العضو الجديد
                            </CardDescription>
                        </CardHeader>

                        <CardContent className="relative p-6 sm:p-8">
                            <Form {...form}>
                                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
                                    {/* Personal Information Section */}
                                    <div className="group space-y-6 rounded-xl border border-gray-100 bg-gradient-to-r from-blue-50/50 to-indigo-50/50 p-6 transition-all duration-500 hover:shadow-md">
                                        <div className="flex items-center gap-3">
                                            <div className="rounded-lg bg-blue-100 p-2 transition-colors duration-300 group-hover:bg-blue-200">
                                                <User className="h-5 w-5 text-blue-600" />
                                            </div>
                                            <h3 className="text-xl font-semibold text-gray-900 transition-colors duration-300 group-hover:text-blue-900">
                                                البيانات الشخصية
                                            </h3>
                                        </div>

                                        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-6 lg:gap-8">
                                            <FormField
                                                control={form.control}
                                                name="name"
                                                render={({ field }) => (
                                                    <FormItem className="group">
                                                        <FormLabel className="flex items-center gap-2 font-medium text-gray-700 transition-colors duration-200 group-focus-within:text-blue-600">
                                                            <User className="h-4 w-4 transition-colors duration-200 group-focus-within:text-blue-600" />
                                                            الاسم الكامل
                                                        </FormLabel>
                                                        <FormControl>
                                                            <Input
                                                                placeholder="أدخل الاسم الكامل"
                                                                className={cn(
                                                                    'touch-friendly-input transition-all duration-300 focus:scale-[1.02] focus:shadow-md',
                                                                    'text-base sm:text-sm', // Prevent zoom on mobile
                                                                    'h-12 sm:h-10', // Larger touch target on mobile
                                                                )}
                                                                {...field}
                                                            />
                                                        </FormControl>
                                                        <FormMessage className="animate-fade-in" />
                                                    </FormItem>
                                                )}
                                            />

                                            <FormField
                                                control={form.control}
                                                name="phone"
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormLabel className="flex flex-row-reverse items-center gap-2 font-medium text-gray-700 transition-colors duration-200 group-focus-within:text-blue-600">
                                                            <Phone className="h-4 w-4 transition-colors duration-200 group-focus-within:text-blue-600" />
                                                            رقم الهاتف
                                                        </FormLabel>
                                                        <FormControl>
                                                            <Input placeholder="01xxxxxxxxx" {...field} />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />

                                            <FormField
                                                control={form.control}
                                                name="gender"
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormLabel>النوع</FormLabel>
                                                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                                                            <FormControl>
                                                                <SelectTrigger>
                                                                    <SelectValue placeholder="اختر النوع" />
                                                                </SelectTrigger>
                                                            </FormControl>
                                                            <SelectContent>
                                                                <SelectItem value="male">ذكر</SelectItem>
                                                                <SelectItem value="female">أنثى</SelectItem>
                                                            </SelectContent>
                                                        </Select>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />

                                            <FormField
                                                control={form.control}
                                                name="birthdate"
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormLabel className="flex items-center gap-2">
                                                            <Calendar className="h-4 w-4" />
                                                            تاريخ الميلاد
                                                        </FormLabel>
                                                        <FormControl>
                                                            <Input type="date" {...field} />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                        </div>
                                    </div>

                                    {/* Academic Information Section */}
                                    <div className="group space-y-6 rounded-xl border border-gray-100 bg-gradient-to-r from-purple-50/50 to-pink-50/50 p-6 transition-all duration-500 hover:shadow-md">
                                        <div className="flex items-center gap-3">
                                            <div className="rounded-lg bg-purple-100 p-2 transition-colors duration-300 group-hover:bg-purple-200">
                                                <GraduationCap className="h-5 w-5 text-purple-600" />
                                            </div>
                                            <h3 className="text-xl font-semibold text-gray-900 transition-colors duration-300 group-hover:text-purple-900">
                                                البيانات الأكاديمية
                                            </h3>
                                        </div>

                                        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-6 lg:grid-cols-3 lg:gap-8">
                                            <FormField
                                                control={form.control}
                                                name="year"
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormLabel>السنة الدراسية</FormLabel>
                                                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                                                            <FormControl>
                                                                <SelectTrigger>
                                                                    <SelectValue placeholder="اختر السنة" />
                                                                </SelectTrigger>
                                                            </FormControl>
                                                            <SelectContent>
                                                                <SelectItem value="1">السنة الأولى</SelectItem>
                                                                <SelectItem value="2">السنة الثانية</SelectItem>
                                                                <SelectItem value="3">السنة الثالثة</SelectItem>
                                                                <SelectItem value="4">السنة الرابعة</SelectItem>
                                                            </SelectContent>
                                                        </Select>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />

                                            <FormField
                                                control={form.control}
                                                name="college"
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormLabel className="flex items-center gap-2">
                                                            <GraduationCap className="h-4 w-4" />
                                                            الكلية
                                                        </FormLabel>
                                                        <FormControl>
                                                            <Input placeholder="اسم الكلية" {...field} />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />

                                            <FormField
                                                control={form.control}
                                                name="department"
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormLabel>القسم</FormLabel>
                                                        <FormControl>
                                                            <Input placeholder="اسم القسم" {...field} />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                        </div>
                                    </div>

                                    {/* Contact Information Section */}
                                    <div className="group space-y-6 rounded-xl border border-gray-100 bg-gradient-to-r from-green-50/50 to-emerald-50/50 p-6 transition-all duration-500 hover:shadow-md">
                                        <div className="flex items-center gap-3">
                                            <div className="rounded-lg bg-green-100 p-2 transition-colors duration-300 group-hover:bg-green-200">
                                                <MapPin className="h-5 w-5 text-green-600" />
                                            </div>
                                            <h3 className="text-xl font-semibold text-gray-900 transition-colors duration-300 group-hover:text-green-900">
                                                معلومات الاتصال
                                            </h3>
                                        </div>

                                        <div className="space-y-6">
                                            <FormField
                                                control={form.control}
                                                name="address"
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormLabel className="flex items-center gap-2">
                                                            <MapPin className="h-4 w-4" />
                                                            العنوان
                                                        </FormLabel>
                                                        <FormControl>
                                                            <Textarea placeholder="العنوان التفصيلي" className="resize-none" rows={3} {...field} />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />

                                            <FormField
                                                control={form.control}
                                                name="facebook_url"
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormLabel className="flex items-center gap-2">
                                                            <Share className="h-4 w-4" />
                                                            رابط الفيسبوك (اختياري)
                                                        </FormLabel>
                                                        <FormControl>
                                                            <Input placeholder="https://facebook.com/username" {...field} />
                                                        </FormControl>
                                                        <FormDescription>رابط الصفحة الشخصية على الفيسبوك (اختياري)</FormDescription>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />

                                            <FormField
                                                control={form.control}
                                                name="first_attendance_date"
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormLabel className="flex items-center gap-2">
                                                            <Calendar className="h-4 w-4" />
                                                            تاريخ أول حضور
                                                        </FormLabel>
                                                        <FormControl>
                                                            <Input type="date" {...field} />
                                                        </FormControl>
                                                        <FormDescription>تاريخ أول مرة حضر فيها العضو (افتراضياً اليوم)</FormDescription>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                        </div>
                                    </div>

                                    {/* Enhanced Submit Button */}
                                    <div className="border-t pt-8">
                                        {submissionState.isSubmitting && (
                                            <div className="mb-6">
                                                <div className="mb-2 flex items-center justify-between">
                                                    <span className="text-sm font-medium text-gray-700">جاري المعالجة...</span>
                                                    <span className="text-sm text-gray-500">{submissionState.progress}%</span>
                                                </div>
                                                <Progress value={submissionState.progress} className="h-2" />
                                            </div>
                                        )}

                                        <div
                                            className={cn(
                                                'flex flex-col gap-3 sm:flex-row sm:gap-4',
                                                isRTL ? 'sm:sm:justify-start' : 'sm:flex-row sm:justify-end',
                                            )}
                                        >
                                            <Button
                                                type="button"
                                                variant="outline"
                                                onClick={resetForm}
                                                disabled={submissionState.isSubmitting}
                                                className={cn(
                                                    'touch-target px-6 py-3 transition-all duration-300 hover:scale-105',
                                                    'h-12 w-full sm:h-auto sm:w-auto', // Full width on mobile
                                                )}
                                            >
                                                <X className={cn('h-4 w-4', isRTL ? 'ml-2' : 'mr-2')} />
                                                إعادة تعيين
                                            </Button>

                                            <Button
                                                type="submit"
                                                disabled={submissionState.isSubmitting || formProgress < 100}
                                                className={cn(
                                                    'relative overflow-hidden px-8 py-3 text-lg font-semibold transition-all duration-300',
                                                    'bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700',
                                                    'hover:scale-105 hover:shadow-lg',
                                                    'disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:scale-100',
                                                    'touch-target h-12 w-full sm:h-auto sm:w-auto', // Full width on mobile
                                                )}
                                            >
                                                {submissionState.isSubmitting ? (
                                                    <>
                                                        <Loader2 className={cn('h-5 w-5 animate-spin', isRTL ? 'ml-2' : 'mr-2')} />
                                                        جاري الحفظ...
                                                    </>
                                                ) : (
                                                    <>
                                                        <Save className={cn('h-5 w-5', isRTL ? 'ml-2' : 'mr-2')} />
                                                        حفظ وإنشاء QR Code
                                                    </>
                                                )}

                                                {/* Animated background on hover */}
                                                <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 transition-opacity duration-300 hover:opacity-100"></div>
                                            </Button>
                                        </div>
                                    </div>
                                </form>
                            </Form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    );
}

AddUserPage.layout = (page: React.ReactElement) => <AppLayout children={page} />;

export default AddUserPage;
