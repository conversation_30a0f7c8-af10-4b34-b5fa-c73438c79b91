import Typesense from 'typesense';

// Typesense client configuration
export const typesenseClient = new Typesense.Client({
    nodes: [
        {
            host: process.env.TYPESENSE_HOST || 'localhost',
            port: parseInt(process.env.TYPESENSE_PORT || '8108'),
            protocol: process.env.TYPESENSE_PROTOCOL || 'http',
        },
    ],
    apiKey: process.env.TYPESENSE_API_KEY || 'xyz',
    connectionTimeoutSeconds: 2,
});

// Collection schemas
export const usersCollectionSchema = {
    name: 'users',
    fields: [
        { name: 'id', type: 'string' },
        { name: 'name', type: 'string' },
        { name: 'phone', type: 'string' },
        { name: 'gender', type: 'string', facet: true },
        { name: 'year', type: 'int32', facet: true },
        { name: 'college', type: 'string', facet: true },
        { name: 'department', type: 'string', facet: true },
        { name: 'birthdate', type: 'string' },
        { name: 'address', type: 'string' },
        { name: 'facebook_url', type: 'string', optional: true },
        { name: 'first_attendance_date', type: 'string' },
        { name: 'qr_code', type: 'string', optional: true },
        { name: 'created_at', type: 'string' },
        { name: 'updated_at', type: 'string' },
    ],
    default_sorting_field: 'name',
};

export const attendanceCollectionSchema = {
    name: 'attendance',
    fields: [
        { name: 'id', type: 'string' },
        { name: 'user_id', type: 'string' },
        { name: 'user_name', type: 'string' },
        { name: 'date', type: 'string', facet: true },
        { name: 'present', type: 'bool', facet: true },
        { name: 'marked_by', type: 'string', facet: true },
        { name: 'created_at', type: 'string' },
    ],
    default_sorting_field: 'created_at',
};

// Search parameters interface
export interface SearchParams {
    q: string;
    query_by: string;
    filter_by?: string;
    sort_by?: string;
    facet_by?: string;
    max_facet_values?: number;
    page?: number;
    per_page?: number;
    highlight_full_fields?: string;
    highlight_affix_num_tokens?: number;
    typo_tokens_threshold?: number;
    drop_tokens_threshold?: number;
    pinned_hits?: string;
    hidden_hits?: string;
}

// Search result interface
export interface SearchResult<T> {
    found: number;
    out_of: number;
    page: number;
    request_params: SearchParams;
    search_time_ms: number;
    hits: Array<{
        document: T;
        highlight: Record<string, any>;
        highlights: Array<{
            field: string;
            snippet: string;
            value: string;
        }>;
        text_match: number;
    }>;
    facet_counts?: Array<{
        field_name: string;
        counts: Array<{
            count: number;
            highlighted: string;
            value: string;
        }>;
    }>;
}

// Initialize collections
export const initializeCollections = async () => {
    try {
        // Check if collections exist, create if they don't
        const collections = await typesenseClient.collections().retrieve();
        const existingCollectionNames = collections.map((col: any) => col.name);

        if (!existingCollectionNames.includes('users')) {
            await typesenseClient.collections().create(usersCollectionSchema);
            console.log('Users collection created');
        }

        if (!existingCollectionNames.includes('attendance')) {
            await typesenseClient.collections().create(attendanceCollectionSchema);
            console.log('Attendance collection created');
        }
    } catch (error) {
        console.error('Error initializing collections:', error);
        // In development, we might want to continue without Typesense
        if (process.env.NODE_ENV === 'development') {
            console.warn('Continuing without Typesense in development mode');
            return false;
        }
        throw error;
    }
    return true;
};

// Index data functions
export const indexUsers = async (users: any[]) => {
    try {
        if (users.length === 0) return;
        
        await typesenseClient.collections('users').documents().import(users, {
            action: 'upsert',
        });
        console.log(`Indexed ${users.length} users`);
    } catch (error) {
        console.error('Error indexing users:', error);
    }
};

export const indexAttendance = async (records: any[]) => {
    try {
        if (records.length === 0) return;
        
        await typesenseClient.collections('attendance').documents().import(records, {
            action: 'upsert',
        });
        console.log(`Indexed ${records.length} attendance records`);
    } catch (error) {
        console.error('Error indexing attendance:', error);
    }
};

// Search functions
export const searchUsers = async (params: Partial<SearchParams>) => {
    try {
        const searchParams: SearchParams = {
            q: params.q || '*',
            query_by: params.query_by || 'name,phone,college,department',
            highlight_full_fields: 'name,college,department',
            highlight_affix_num_tokens: 3,
            per_page: params.per_page || 50,
            page: params.page || 1,
            typo_tokens_threshold: 1,
            drop_tokens_threshold: 1,
            ...params,
        };

        const result = await typesenseClient.collections('users').documents().search(searchParams);
        return result as SearchResult<any>;
    } catch (error) {
        console.error('Error searching users:', error);
        throw error;
    }
};

export const searchAttendance = async (params: Partial<SearchParams>) => {
    try {
        const searchParams: SearchParams = {
            q: params.q || '*',
            query_by: params.query_by || 'user_name,date,marked_by',
            highlight_full_fields: 'user_name,marked_by',
            highlight_affix_num_tokens: 3,
            per_page: params.per_page || 50,
            page: params.page || 1,
            typo_tokens_threshold: 1,
            drop_tokens_threshold: 1,
            ...params,
        };

        const result = await typesenseClient.collections('attendance').documents().search(searchParams);
        return result as SearchResult<any>;
    } catch (error) {
        console.error('Error searching attendance:', error);
        throw error;
    }
};

// Utility functions for building filters
export const buildFilter = (filters: Record<string, any>) => {
    const filterParts: string[] = [];
    
    Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '' && value !== 'all') {
            if (Array.isArray(value)) {
                if (value.length > 0) {
                    filterParts.push(`${key}:[${value.join(',')}]`);
                }
            } else if (typeof value === 'string') {
                filterParts.push(`${key}:=${value}`);
            } else if (typeof value === 'number') {
                filterParts.push(`${key}:=${value}`);
            } else if (typeof value === 'boolean') {
                filterParts.push(`${key}:=${value}`);
            }
        }
    });
    
    return filterParts.join(' && ');
};

// Get search suggestions
export const getSearchSuggestions = async (query: string, collection: 'users' | 'attendance') => {
    try {
        if (!query || query.length < 2) return [];
        
        const searchParams: SearchParams = {
            q: query,
            query_by: collection === 'users' ? 'name,college,department' : 'user_name,marked_by',
            per_page: 5,
            page: 1,
        };

        const result = await typesenseClient.collections(collection).documents().search(searchParams);
        
        return result.hits.map((hit: any) => ({
            text: hit.document.name || hit.document.user_name,
            value: hit.document.id,
            highlight: hit.highlight,
        }));
    } catch (error) {
        console.error('Error getting suggestions:', error);
        return [];
    }
};
